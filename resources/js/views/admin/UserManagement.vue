<template>
  <div class="user-management">
    <h1 class="text-2xl font-bold mb-6">User Management</h1>

    <div v-if="loading" class="text-center py-4">
      <p>Loading...</p>
    </div>

    <div v-else-if="isAdmin">
      <div class="mb-4 flex justify-between items-center">
        <button 
          @click="openCreateModal" 
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Add New User
        </button>
      </div>

      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Building</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in users" :key="user.id">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ user.name }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.email }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.role }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.building ? user.building.name : '-' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button @click="editUser(user)" class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</button>
                <button @click="deleteUser(user.id)" class="text-red-600 hover:text-red-900">Delete</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Modal for Create/Edit User -->
      <div v-if="showModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <h2 class="text-xl font-bold mb-4">{{ editMode ? 'Edit User' : 'Add New User' }}</h2>
          <form @submit.prevent="saveUser">
            <div class="mb-4">
              <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
              <input type="text" id="name" v-model="currentUser.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
            </div>
            <div class="mb-4">
              <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
              <input type="email" id="email" v-model="currentUser.email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
            </div>
            <div class="mb-4" v-if="!editMode">
              <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
              <input type="password" id="password" v-model="currentUser.password" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
            </div>
            <div class="mb-4" v-if="!editMode">
              <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
              <input type="password" id="password_confirmation" v-model="currentUser.password_confirmation" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
            </div>
            <div class="mb-4">
              <label for="apartment_number" class="block text-sm font-medium text-gray-700">Apartment Number</label>
              <input type="text" id="apartment_number" v-model="currentUser.apartment_number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
            </div>
            <div class="mb-4" v-if="isSuperAdmin">
              <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
              <select id="role" v-model="currentUser.role" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                <option value="super_admin">Super Admin</option>
                <option value="admin">Admin</option>
                <option value="neighbor">Neighbor</option>
              </select>
            </div>
            <div class="mb-4" v-else>
              <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
              <select id="role" v-model="currentUser.role" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                <option value="admin">Admin</option>
                <option value="neighbor">Neighbor</option>
              </select>
            </div>
            <div class="mb-4" v-if="currentUser.role !== 'super_admin' && isAdmin">
              <label for="building_id" class="block text-sm font-medium text-gray-700">Building</label>
              <select id="building_id" v-model="currentUser.building_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                <option :value="null">None</option>
                <option v-for="building in buildings" :key="building.id" :value="building.id">{{ building.name }}</option>
              </select>
            </div>
            <div class="mb-4" v-else-if="currentUser.role !== 'super_admin'">
              <label for="building_id" class="block text-sm font-medium text-gray-700">Building</label>
              <select id="building_id" v-model="currentUser.building_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" disabled>
                <option :value="null">None</option>
                <option v-for="building in buildings" :key="building.id" :value="building.id">{{ building.name }}</option>
              </select>
            </div>
            <div class="flex justify-end">
              <button type="button" @click="closeModal" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">Cancel</button>
              <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Save</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div v-else class="text-center py-8">
      <div class="text-red-600 text-lg font-semibold mb-2">
        Access Denied: You do not have permission to manage users.
      </div>
      <div class="text-gray-600 text-sm">
        <p>Current user role: {{ user?.role || 'Unknown' }}</p>
        <p>Required role: admin or super_admin</p>
        <p v-if="user">User ID: {{ user.id }}</p>
        <p v-if="user">Building ID: {{ user.building_id || 'None' }}</p>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'UserManagement',
  data() {
    return {
      users: [],
      buildings: [],
      showModal: false,
      editMode: false,
      currentUser: {
        id: null,
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        apartment_number: '',
        role: 'neighbor',
        building_id: null
      },
      user: null,
      isAdmin: false,
      isSuperAdmin: false,
      adminBuildingId: null,
      loading: true
    };
  },
  async mounted() {
    await this.fetchUser();
    if (this.isAdmin) {
      this.fetchUsers();
      this.fetchBuildings();
    }
  },
  methods: {
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isAdmin = this.user.role === 'admin' || this.user.role === 'super_admin';
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.adminBuildingId = this.user.building_id;
        console.log('User fetched:', this.user);
        console.log('Is Admin:', this.isAdmin);
      } catch (error) {
        console.error('Error fetching user:', error);
        // If there's an error fetching user, redirect to login
        this.$router.push('/login');
      } finally {
        this.loading = false;
      }
    },
    fetchUsers() {
      this.$axios.get('/admin/users')
        .then(response => {
          let usersData = response.data.data || response.data;
          if (!this.isSuperAdmin && this.adminBuildingId) {
            this.users = usersData.filter(user => user.building_id === this.adminBuildingId);
          } else {
            this.users = usersData;
          }
        })
        .catch(error => {
          console.error('Error fetching users:', error);
        });
    },
    fetchBuildings() {
      this.$axios.get('/buildings')
        .then(response => {
          this.buildings = response.data;
        })
        .catch(error => {
          console.error('Error fetching buildings:', error);
        });
    },
    openCreateModal() {
      this.editMode = false;
      this.currentUser = {
        id: null,
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        apartment_number: '',
        role: 'neighbor',
        building_id: null
      };
      this.showModal = true;
    },
    editUser(user) {
      this.editMode = true;
      this.currentUser = { ...user, password: '', password_confirmation: '' };
      this.showModal = true;
    },
    closeModal() {
      this.showModal = false;
    },
    saveUser() {
      let data = { ...this.currentUser };
      if (!this.editMode) {
        if (data.password !== data.password_confirmation) {
          alert('Passwords do not match!');
          return;
        }
      } else {
        if (!data.password) {
          delete data.password;
          delete data.password_confirmation;
        }
      }
      
      if (data.role === 'super_admin') {
        data.building_id = null;
      } else if (!this.isSuperAdmin && this.adminBuildingId) {
        data.building_id = this.adminBuildingId;
      }

      if (this.editMode) {
        this.$axios.put(`/admin/users/${this.currentUser.id}`, data)
          .then(response => {
            const index = this.users.findIndex(u => u.id === response.data.user.id);
            this.users[index] = response.data.user;
            this.closeModal();
          })
          .catch(error => {
            console.error('Error updating user:', error);
            alert('Failed to update user: ' + (error.response?.data?.message || error.message));
          });
      } else {
        this.$axios.post('/admin/users', data)
          .then(response => {
            this.users.push(response.data.user);
            this.closeModal();
          })
          .catch(error => {
            console.error('Error creating user:', error);
            alert('Failed to create user: ' + (error.response?.data?.message || error.message));
          });
      }
    },
    deleteUser(id) {
      if (confirm('Are you sure you want to delete this user?')) {
        this.$axios.delete(`/admin/users/${id}`)
          .then(() => {
            this.users = this.users.filter(user => user.id !== id);
          })
          .catch(error => {
            console.error('Error deleting user:', error);
          });
      }
    }
  }
};
</script>

<style scoped>
.user-management {
  padding: 20px;
}
</style>
