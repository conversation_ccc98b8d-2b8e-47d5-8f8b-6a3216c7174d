<template>
  <dashboard-layout title="My Building">
    <template #sidebar>
      <router-link
        to="/admin/expenses"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
      <router-link
        to="/admin/incomes"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Incomes
      </router-link>
      <router-link
        to="/admin/users"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Users
      </router-link>
      <router-link
        to="/admin/my-building"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        My Building
      </router-link>
    </template>

    <div v-if="loading" class="text-center py-4">
      <p>Loading...</p>
    </div>

    <div v-else-if="building" class="max-w-4xl mx-auto">
      <!-- Building Info Card -->
      <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-900">Building Information</h2>
          <button
            @click="editBuilding"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Edit Building
          </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-sm font-medium text-gray-500">Building Name</h3>
            <p class="mt-1 text-sm text-gray-900">{{ building.name }}</p>
          </div>
          
          <div>
            <h3 class="text-sm font-medium text-gray-500">Monthly Fee</h3>
            <p class="mt-1 text-sm text-gray-900">₪{{ parseFloat(building.monthly_fee).toFixed(2) }}</p>
          </div>
          
          <div>
            <h3 class="text-sm font-medium text-gray-500">Address</h3>
            <p class="mt-1 text-sm text-gray-900">{{ building.address || 'Not specified' }}</p>
          </div>
          
          <div>
            <h3 class="text-sm font-medium text-gray-500">City</h3>
            <p class="mt-1 text-sm text-gray-900">{{ building.city || 'Not specified' }}</p>
          </div>
          
          <div>
            <h3 class="text-sm font-medium text-gray-500">Country</h3>
            <p class="mt-1 text-sm text-gray-900">{{ building.country || 'Not specified' }}</p>
          </div>
          
          <div>
            <h3 class="text-sm font-medium text-gray-500">Postal Code</h3>
            <p class="mt-1 text-sm text-gray-900">{{ building.postal_code || 'Not specified' }}</p>
          </div>
        </div>
        
        <div v-if="building.description" class="mt-6">
          <h3 class="text-sm font-medium text-gray-500">Description</h3>
          <p class="mt-1 text-sm text-gray-900">{{ building.description }}</p>
        </div>
      </div>

      <!-- Monthly Expense Generation -->
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Monthly Expense Generation</h2>
        <p class="text-sm text-gray-600 mb-4">
          Generate monthly expenses for all neighbors in your building. 
          Each neighbor will be charged ₪{{ parseFloat(building.monthly_fee).toFixed(2) }}.
        </p>
        
        <div class="flex items-center space-x-4">
          <button
            @click="generateMonthlyExpenses"
            :disabled="generatingExpenses"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-gray-400"
          >
            {{ generatingExpenses ? 'Generating...' : `Generate Monthly Expenses (₪${parseFloat(building.monthly_fee).toFixed(2)})` }}
          </button>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8">
      <p class="text-red-500">No building assigned to your account.</p>
    </div>

    <!-- Edit Modal -->
    <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
      <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
        <div class="flex justify-between items-center p-6 border-b">
          <h2 class="text-xl font-semibold text-gray-900">Edit Building</h2>
          <button @click="closeEditModal" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <!-- Modal body -->
        <div class="p-6">
          <building-form
            :building="building"
            :is-edit="true"
            @success="handleEditSuccess"
            @error="handleEditError"
            @cancel="closeEditModal"
          />
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import BuildingForm from '../../components/BuildingForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    BuildingForm,
    Notification
  },
  data() {
    return {
      loading: false,
      building: null,
      showEditModal: false,
      generatingExpenses: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  mounted() {
    this.loadBuilding();
  },
  methods: {
    async loadBuilding() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/my-building');
        this.building = response.data;
      } catch (error) {
        console.error('Error loading building:', error);
        if (error.response?.status !== 404) {
          this.showError('Error', 'Failed to load building information');
        }
      } finally {
        this.loading = false;
      }
    },
    editBuilding() {
      this.showEditModal = true;
    },
    closeEditModal() {
      this.showEditModal = false;
    },
    handleEditSuccess(data) {
      this.showSuccess('Success', 'Building updated successfully');
      this.closeEditModal();
      this.loadBuilding();
    },
    handleEditError(message) {
      this.showError('Error', message);
    },
    async generateMonthlyExpenses() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const formattedMonth = currentMonth.toString().padStart(2, '0');
      const monthlyFee = parseFloat(this.building.monthly_fee).toFixed(2);

      if (confirm(`Generate monthly expenses (₪${monthlyFee}) for all neighbors in your building for ${formattedMonth}/${currentYear}?`)) {
        this.generatingExpenses = true;
        try {
          await this.$axios.post('/expenses/generate-monthly', {
            month: formattedMonth,
            year: currentYear.toString()
          });

          this.showSuccess('Generated', 'Monthly expenses generated successfully');
        } catch (error) {
          this.showError('Generation Failed', error.response?.data?.message || 'Failed to generate monthly expenses');
        } finally {
          this.generatingExpenses = false;
        }
      }
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
