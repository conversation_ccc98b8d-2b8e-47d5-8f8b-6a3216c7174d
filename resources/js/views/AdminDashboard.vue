<template>
  <dashboard-layout title="Admin Dashboard">
    <template #sidebar>
      <router-link
        to="/admin/expenses"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
      <router-link
        to="/admin/incomes"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Incomes
      </router-link>
      <router-link
        to="/admin/users"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Users
      </router-link>
      <router-link
        v-if="isSuperAdmin"
        to="/admin/buildings"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Buildings
      </router-link>
    </template>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ isSuperAdmin ? 'Total Buildings' : 'Total Expenses' }}</h3>
        <p class="text-3xl font-bold text-blue-600">{{ isSuperAdmin ? totalBuildings : '₪' + totalExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ isSuperAdmin ? 'Total Admins' : 'Total Income' }}</h3>
        <p class="text-3xl font-bold text-green-600">{{ isSuperAdmin ? totalAdmins : '₪' + totalIncome.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ isSuperAdmin ? 'Total Neighbors' : 'Total Users' }}</h3>
        <p class="text-3xl font-bold text-purple-600">{{ isSuperAdmin ? totalNeighbors : totalUsers }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ isSuperAdmin ? 'Active Buildings' : 'Outstanding Balance' }}</h3>
        <p class="text-3xl font-bold text-orange-600">{{ isSuperAdmin ? activeBuildings : '₪' + outstandingBalance.toFixed(2) }}</p>
      </div>
    </div>

    <!-- Super Admin Tables -->
    <div v-if="isSuperAdmin" class="space-y-6">
      <!-- Buildings Table -->
      <data-table
        title="Buildings"
        :columns="buildingColumns"
        :items="buildings"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex space-x-2">
            <router-link
              :to="`/admin/buildings/${item.id}/edit`"
              class="text-indigo-600 hover:text-indigo-900"
            >
              Edit
            </router-link>
            <button
              @click="deleteBuilding(item)"
              class="text-red-600 hover:text-red-900"
            >
              Delete
            </button>
          </div>
        </template>
        <template #header-actions>
          <router-link
            to="/admin/buildings/create"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Add Building
          </router-link>
        </template>
      </data-table>

      <!-- Admins Table -->
      <data-table
        title="Admins"
        :columns="adminColumns"
        :items="admins"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex space-x-2">
            <button
              @click="editAdmin(item)"
              class="text-indigo-600 hover:text-indigo-900"
            >
              Edit
            </button>
            <button
              @click="deleteAdmin(item)"
              class="text-red-600 hover:text-red-900"
            >
              Delete
            </button>
          </div>
        </template>
        <template #header-actions>
          <router-link
            to="/admin/users/create"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Add Admin
          </router-link>
        </template>
      </data-table>
    </div>

    <!-- Regular Admin Table -->
    <div v-else>
      <data-table
        title="Neighbor Financial Summary"
        :columns="neighborColumns"
        :items="neighborSummary"
        :loading="loading"
      />
    </div>
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../components/DashboardLayout.vue';
import DataTable from '../components/DataTable.vue';

export default {
  components: {
    DashboardLayout,
    DataTable
  },
  data() {
    return {
      loading: false,
      totalExpenses: 0,
      totalIncome: 0,
      totalUsers: 0,
      totalBuildings: 0,
      totalAdmins: 0,
      totalNeighbors: 0,
      activeBuildings: 0,
      allExpenses: [],
      allIncomes: [],
      buildings: [],
      admins: [],
      neighborColumns: [
        { key: 'name', label: 'Neighbor' },
        { key: 'apartment_number', label: 'Apartment' },
        { key: 'total_expenses', label: 'Total Expenses' },
        { key: 'total_income', label: 'Total Income' },
        { key: 'outstanding_balance', label: 'Outstanding Balance' }
      ],
      buildingColumns: [
        { key: 'name', label: 'Building Name' },
        { key: 'city', label: 'City' },
        { key: 'monthly_fee', label: 'Monthly Fee' },
        { key: 'admin_count', label: 'Admins' },
        { key: 'neighbor_count', label: 'Neighbors' }
      ],
      adminColumns: [
        { key: 'name', label: 'Name' },
        { key: 'email', label: 'Email' },
        { key: 'building.name', label: 'Building' },
        { key: 'created_at', label: 'Created' }
      ],
      isSuperAdmin: false
    };
  },
  computed: {
    outstandingBalance() {
      return this.totalExpenses - this.totalIncome;
    },
    neighborSummary() {
      // Create a map to group expenses and incomes by user
      const userMap = new Map();

      // Process expenses
      this.allExpenses.forEach(expense => {
        if (expense.user) {
          const userId = expense.user.id;
          if (!userMap.has(userId)) {
            userMap.set(userId, {
              id: userId,
              name: expense.user.name,
              apartment_number: expense.user.apartment_number,
              total_expenses: 0,
              total_income: 0
            });
          }
          userMap.get(userId).total_expenses += parseFloat(expense.amount);
        }
      });

      // Process incomes
      this.allIncomes.forEach(income => {
        if (income.user) {
          const userId = income.user.id;
          if (!userMap.has(userId)) {
            userMap.set(userId, {
              id: userId,
              name: income.user.name,
              apartment_number: income.user.apartment_number,
              total_expenses: 0,
              total_income: 0
            });
          }
          userMap.get(userId).total_income += parseFloat(income.amount);
        }
      });

      // Convert to array and calculate outstanding balance
      return Array.from(userMap.values()).map(user => ({
        ...user,
        outstanding_balance: user.total_expenses - user.total_income
      })).sort((a, b) => a.apartment_number.localeCompare(b.apartment_number));
    }
  },
  async created() {
    await this.loadDashboardData();
  },
  methods: {
    async loadDashboardData() {
      this.loading = true;
      try {
        // Load user data to check role
        const userResponse = await this.$axios.get('/user');
        if (userResponse.data) {
          this.isSuperAdmin = userResponse.data.role === 'super_admin';
        }

        if (this.isSuperAdmin) {
          // Load buildings data for super admin
          const buildingsResponse = await this.$axios.get('/buildings');
          if (buildingsResponse.data) {
            this.buildings = buildingsResponse.data.map(building => ({
              ...building,
              monthly_fee: `₪${parseFloat(building.monthly_fee || 0).toFixed(2)}`,
              admin_count: 0, // Will be calculated from users
              neighbor_count: 0 // Will be calculated from users
            }));
            this.totalBuildings = this.buildings.length;
            this.activeBuildings = this.buildings.filter(b => b.admin_count > 0).length;
          }

          // Load all users data for super admin
          const usersResponse = await this.$axios.get('/admin/users');
          if (usersResponse.data.data) {
            const allUsers = usersResponse.data.data;

            // Filter admins
            this.admins = allUsers.filter(user => user.role === 'admin');
            this.totalAdmins = this.admins.length;

            // Count neighbors
            this.totalNeighbors = allUsers.filter(user => user.role === 'neighbor').length;

            // Calculate building user counts
            this.buildings.forEach(building => {
              building.admin_count = allUsers.filter(user =>
                user.building_id === building.id && user.role === 'admin'
              ).length;
              building.neighbor_count = allUsers.filter(user =>
                user.building_id === building.id && user.role === 'neighbor'
              ).length;
            });

            // Update active buildings count
            this.activeBuildings = this.buildings.filter(b => b.admin_count > 0).length;
          }
        } else {
          // Load regular admin data
          // Load all expenses data
          const expensesResponse = await this.$axios.get('/expenses');
          if (expensesResponse.data.data) {
            this.allExpenses = expensesResponse.data.data;
            this.totalExpenses = expensesResponse.data.data.reduce((total, expense) =>
              total + parseFloat(expense.amount), 0);
          }

          // Load all incomes data
          const incomesResponse = await this.$axios.get('/incomes');
          if (incomesResponse.data.data) {
            this.allIncomes = incomesResponse.data.data;
            this.totalIncome = incomesResponse.data.data.reduce((total, income) =>
              total + parseFloat(income.amount), 0);
          }

          // Load users data
          const usersResponse = await this.$axios.get('/admin/users');
          if (usersResponse.data.data) {
            this.totalUsers = usersResponse.data.data.length;
          }
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        this.loading = false;
      }
    },
    async deleteBuilding(building) {
      if (confirm(`Are you sure you want to delete building "${building.name}"?`)) {
        try {
          await this.$axios.delete(`/buildings/${building.id}`);
          this.buildings = this.buildings.filter(b => b.id !== building.id);
          this.totalBuildings = this.buildings.length;
        } catch (error) {
          console.error('Error deleting building:', error);
          alert('Failed to delete building');
        }
      }
    },
    editAdmin(admin) {
      // Navigate to user edit page or open modal
      this.$router.push(`/admin/users/${admin.id}/edit`);
    },
    async deleteAdmin(admin) {
      if (confirm(`Are you sure you want to delete admin "${admin.name}"?`)) {
        try {
          await this.$axios.delete(`/admin/users/${admin.id}`);
          this.admins = this.admins.filter(a => a.id !== admin.id);
          this.totalAdmins = this.admins.length;
        } catch (error) {
          console.error('Error deleting admin:', error);
          alert('Failed to delete admin');
        }
      }
    }
  }
};
</script>
