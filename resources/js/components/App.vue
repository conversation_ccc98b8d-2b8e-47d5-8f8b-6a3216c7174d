<template>
    <div class="min-h-screen bg-gray-100">
        <nav class="bg-white shadow-lg mb-6">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between h-16 items-center">
                    <div class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-800">Laravel + Vue App</h1>
                        <div v-if="isAuthenticated" class="ml-10 flex items-center space-x-4">
                            <router-link 
                                v-if="isAdmin"
                                to="/admin" 
                                class="text-gray-600 hover:text-gray-900"
                                active-class="text-indigo-600"
                            >
                                Admin Dashboard
                            </router-link>
                            <router-link 
                                v-if="isNeighbor"
                                to="/neighbor" 
                                class="text-gray-600 hover:text-gray-900"
                                active-class="text-indigo-600"
                            >
                                Neighbor Dashboard
                            </router-link>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <template v-if="isAuthenticated">
                            <span class="text-gray-600 mr-4">{{ user?.name }}</span>
                            <button 
                                @click="logout" 
                                class="text-red-600 hover:text-red-800"
                            >
                                Logout
                            </button>
                        </template>
                        <template v-else>
                            <router-link 
                                to="/login" 
                                class="text-gray-600 hover:text-gray-900 mr-4"
                                active-class="text-indigo-600"
                            >
                                Login
                            </router-link>
                            <router-link 
                                to="/register" 
                                class="text-gray-600 hover:text-gray-900"
                                active-class="text-indigo-600"
                            >
                                Register
                            </router-link>
                        </template>
                    </div>
                </div>
            </div>
        </nav>
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <router-view />
        </main>
    </div>
</template>

<script>
export default {
    name: 'App',
    data() {
        return {
            user: null
        };
    },
    computed: {
        isAuthenticated() {
            return !!localStorage.getItem('token');
        },
        isAdmin() {
            return this.user?.role === 'admin' || this.user?.role === 'super_admin';
        },
        isNeighbor() {
            return this.user?.role === 'neighbor';
        }
    },
    created() {
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
    },
    methods: {
        async logout() {
            try {
                await this.$axios.post('/logout');
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                this.user = null;
                this.$router.push('/login');
            }
        }
    }
}
</script> 