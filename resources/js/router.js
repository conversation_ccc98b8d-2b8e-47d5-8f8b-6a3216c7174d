import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('./views/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('./views/Register.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/registration-success',
    name: 'RegistrationSuccess',
    component: () => import('./views/RegistrationSuccess.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/admin',
    name: 'AdminDashboard',
    component: () => import('./views/AdminDashboard.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/neighbor',
    name: 'NeighborDashboard',
    component: () => import('./views/NeighborDashboard.vue'),
    meta: { requiresAuth: true, role: 'neighbor' }
  },
  {
    path: '/admin/users',
    name: 'admin.users',
    component: () => import('./views/admin/UserManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/users/create',
    name: 'admin.users.create',
    component: () => import('./views/admin/CreateUser.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/my-building',
    name: 'admin.my-building',
    component: () => import('./views/admin/MyBuilding.vue'),
    meta: { requiresAuth: true, role: 'admin' }
  },
  {
    path: '/admin/expenses',
    name: 'admin.expenses',
    component: () => import('./views/admin/ExpenseManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/expenses/create',
    name: 'admin.expenses.create',
    component: () => import('./views/admin/CreateExpense.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/incomes',
    name: 'IncomeManagement',
    component: () => import('./views/admin/IncomeManagement.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/incomes/create',
    name: 'CreateIncome',
    component: () => import('./views/admin/CreateIncome.vue'),
    meta: { requiresAuth: true, role: ['admin', 'super_admin'] }
  },
  {
    path: '/admin/buildings',
    name: 'admin.buildings',
    component: () => import('./views/admin/BuildingManagement.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/admin/buildings/create',
    name: 'admin.buildings.create',
    component: () => import('./views/admin/CreateBuilding.vue'),
    meta: { requiresAuth: true, role: 'super_admin' }
  },
  {
    path: '/',
    redirect: '/login'
  }
];

const router = createRouter({
  history: createWebHistory('/lajnet-amara/'),
  routes
});

  // Navigation Guard
router.beforeEach((to, _from, next) => {
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || 'null');

  console.log('Router guard - navigating to:', to.name, to.path);
  console.log('Router guard - user:', user);
  console.log('Router guard - token exists:', !!token);

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!token) {
      // No token, redirect to login
      console.log('Router guard - no token, redirecting to login');
      next({ name: 'Login' });
      return;
    }

    // Check role-based access
    if (to.meta.role) {
      if (to.meta.role === 'super_admin' && user?.role !== 'super_admin') {
        // Not a super admin, redirect to appropriate dashboard
        console.log('Router guard - not super admin, redirecting. Required:', to.meta.role, 'User role:', user?.role);
        next({ name: (user?.role === 'admin' || user?.role === 'super_admin') ? 'AdminDashboard' : 'NeighborDashboard' });
        return;
      } else if (Array.isArray(to.meta.role) && !to.meta.role.includes(user?.role)) {
        // Role not in allowed list, redirect to appropriate dashboard
        console.log('Router guard - role not allowed, redirecting. Required:', to.meta.role, 'User role:', user?.role);
        next({ name: (user?.role === 'admin' || user?.role === 'super_admin') ? 'AdminDashboard' : 'NeighborDashboard' });
        return;
      } else if (!Array.isArray(to.meta.role) && to.meta.role === 'admin' && user?.role !== 'admin' && user?.role !== 'super_admin') {
        // Not an admin or super admin, redirect to neighbor dashboard
        console.log('Router guard - not admin, redirecting. Required:', to.meta.role, 'User role:', user?.role);
        next({ name: 'NeighborDashboard' });
        return;
      }
    }
  }

  // Check if route requires guest (not logged in)
  if (to.meta.requiresGuest) {
    if (token) {
      // User is logged in, redirect to appropriate dashboard
      console.log('Router guard - user logged in, redirecting from guest route');
      next({ name: (user?.role === 'admin' || user?.role === 'super_admin') ? 'AdminDashboard' : 'NeighborDashboard' });
      return;
    }
  }

  console.log('Router guard - allowing navigation');
  next();
});

export default router;
